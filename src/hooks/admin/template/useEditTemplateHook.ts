import { useDisclosure } from '@chakra-ui/react';
import { useEffect, useState } from 'react';
import * as Yup from 'yup';
import { useFormik } from 'formik';
import { toaster } from '@/components/ui/toaster';
import { replacementTags } from '@/utils/config';
import { ITemplates } from '@/shared/interface/templates';
import { useUpdateTemplateAPI } from '@/api/template/update-template';
import { useDeleteTemplateApi } from '@/api/template/delete-template';

export const useEditTemplateHook = ({ row }: { row: ITemplates }) => {
  const { open: isOpen, onClose, onOpen } = useDisclosure();

  const {
    open: isDeleteOpen,
    onClose: onDeleteClose,
    onOpen: onDeleteOpen,
  } = useDisclosure();

  const [loading, setLoading] = useState({
    update: false,
    delete: false,
  });
  const [selectedReplacementTag, setSelectedReplacementTag] = useState<
    string[]
  >([]);

  const { mutateAsync: updateMutateAsync } = useUpdateTemplateAPI();
  const { mutateAsync: deleteMutateAsync } = useDeleteTemplateApi();

  const initialValues = {
    name: '',
    content: '',
  };

  // console.log('slp', slp);
  // console.log("Organization ID:", slp?.organization_id);

  const validationSchema = Yup.object({
    name: Yup.string().trim().required('Template name is required'),
    content: Yup.string().trim().required('Content is required'),
  });

  const {
    values,
    handleSubmit,
    errors,
    touched,
    handleChange,
    setFieldValue,
    setValues,
    resetForm,
  } = useFormik({
    initialValues: initialValues,
    validationSchema,
    onSubmit: async (values) => {
      try {
        setLoading((prev) => {
          return {
            ...prev,
            update: true,
          };
        });

        const payload = {
          id: row?.id,
          name: values.name,
          content: values.content,
        };
        await updateMutateAsync(payload);

        onClose();
        resetForm();
        // toaster.create({
        //     description: 'Template Created Successfully',
        //     type: 'success',
        // });
      } catch (error) {
        // setLoading(false);
        toaster.create({
          description: 'Something went wrong.',
          type: 'error',
        });
        console.error(error);
      } finally {
        setLoading((prev) => {
          return {
            ...prev,
            update: false,
          };
        });
      }
    },
  });

  const handleDeleteTemplate = async () => {
    try {
      setLoading((prev) => {
        return {
          ...prev,
          delete: true,
        };
      });
      await deleteMutateAsync({ id: row.id });
    } catch (error) {
      console.log('error', error);
    } finally {
      setLoading((prev) => {
        return {
          ...prev,
          delete: false,
        };
      });
      onDeleteClose();
    }
  };

  const handleReplacementTag = (value: string) => {
    if (selectedReplacementTag.indexOf(value) < 0) {
      const newReplacementTag = selectedReplacementTag;
      newReplacementTag.push(value);
      setSelectedReplacementTag(() => [...newReplacementTag]);
    }
  };

  useEffect(() => {
    if (row.id) {
      setFieldValue('name', row.name);
      setFieldValue('content', row.content);
    }
  }, [row?.id, row?.name, row?.content, setFieldValue, isOpen]);

  return {
    isOpen,
    onClose,
    onOpen,
    replacementTags,
    handleReplacementTag,
    loading,
    values,
    handleSubmit,
    errors,
    touched,
    handleChange,
    setFieldValue,
    setValues,
    isDeleteOpen,
    onDeleteClose,
    onDeleteOpen,
    handleDeleteTemplate,
  };
};
