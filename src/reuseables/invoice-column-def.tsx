import EditInvoice from '@/app/(dashboard)/billing/invoices/EditInvoice';
import LinkSlpModal from '@/app/(dashboard)/billing/invoices/LinkSlpModal';
import LinkedInvoiceDetails from '@/app/(dashboard)/invoices/LinkedInvoiceDetails';
import Status from '@/components/elements/status/Status';
import { Tooltip } from '@/components/ui/tooltip';
import { getSessionColor } from '@/utils/color-helper';
import { Box, Center, Text } from '@chakra-ui/react';
import { createColumnHelper } from '@tanstack/react-table';
import { useSearchParams } from 'next/navigation';

import { formatMoney } from '@/components/elements/format-money/FormatMoney';
// import moment from 'moment';
import Link from 'next/link';
import { Fragment } from 'react';

const ClientNameCell = ({ row }: { row: any }) => {
  const searchParams = useSearchParams();
  const organizationId = searchParams.get('organization_id');
  const client = row.original?.name || row.original?.clients?.display_name;
  const clientId = row.original?.client_id;

  if (!client || !clientId) {
    return null;
  }

  const href = organizationId
    ? `/contacts/${clientId}?organization_id=${organizationId}`
    : `/contacts/${clientId}`;

  return (
    <Box
      onClick={(e) => e.stopPropagation()}
      color={'rgb(79 70 229)'}
      fontWeight={500}
    >
      <Link href={href}>{client}</Link>
    </Box>
  );
};

const columnHelper = createColumnHelper<any>();

export const createInvoiceColumnDef = (
  org_id: any,
  page_section: any,
  clientId?: any,
  filterStatus?: string,
  formatDate?: any
) => {
  const isSpeakFluent = org_id === 1;
  // const isBillingPage = page_section === 'billing';
  const isContactPage = page_section === 'contact';
  const showUnpaidColumns =
    filterStatus === 'UNPAID' ||
    filterStatus === 'AWAITING_PAYMENT' ||
    filterStatus === 'PARTIALLY_PAID';

  // Client Name column
  const clientNameColumn = columnHelper.accessor('name', {
    cell: (props) => {
      const isVoid = props.row.original.status === 'Void';
      return isContactPage ? (
        <Box data-no-row-click="true" color={isVoid ? 'gray.100' : 'black'}>
          {props.getValue()}
        </Box>
      ) : (
        <Fragment>
          {props.row.original?.client_id ? (
            <ClientNameCell row={props.row} />
          ) : (
            <Box data-no-row-click="true" color="red" fontWeight={500}>
              {props.getValue()}
            </Box>
          )}
        </Fragment>
      );
    },
    header: 'Client Name',
    id: 'client-name',
  });

  // Status column
  const statusColumn = columnHelper.accessor('status', {
    cell: (info) => <Status name={info.getValue() as string} />,
    header: 'Status',
    id: 'status',
  });

  // Date column for speak fluent
  const InvoiceDateColumn = columnHelper.accessor('invoice_date', {
    cell: (info) => {
      const isVoid = info.row.original.status === 'Void';
      return (
        <Box color={isVoid ? 'gray.100' : 'black'}>
          {/* {moment(info.getValue()).utc().format('MMMM D, YYYY')} */}

          {formatDate?.(info.getValue()?.split('T')[0])}
        </Box>
      );
    },
    header: 'Invoice Date',
    id: 'invoice_date',
    sortingFn: 'datetime',
  });

  // Date column for none speak fluent
  const dueDateColumn = columnHelper.accessor('due_date', {
    cell: (info) => {
      const isVoid = info.row.original.status === 'Void';
      return (
        <Box color={isVoid ? 'gray.100' : 'black'}>
          {/* {moment(info.getValue()).utc().format('MMMM D, YYYY')} */}

          {formatDate?.(info.getValue()?.split('T')[0])}
        </Box>
      );
    },
    header: 'Due Date',
    id: 'due_date',
    sortingFn: 'datetime',
  });

  // No. (Invoice Number) column
  const invoiceNumberColumn = columnHelper.accessor('invoice_number', {
    cell: (info) => {
      const isVoid = info.row.original.status === 'Void';
      return <Box color={isVoid ? 'gray.100' : 'black'}>{info.getValue()}</Box>;
    },
    header: 'No.',
    id: 'invoice-number',
  });

  // Amount (Total Price) column
  const amountColumn = columnHelper.accessor('total_price', {
    cell: (info) => {
      return (
        <Box minW={'3.5rem'}>
          {formatMoney(info.getValue(), {
            currencyCode: info.row.original?.currency_code,
          })}
        </Box>
      );
    },
    header: 'Amount',
    id: 'total_price',
  });

  // SpeakFluent specific columns
  const speakFluentColumns = isSpeakFluent
    ? [
        columnHelper.display({
          cell: (props) => {
            const sessionType = props.row.original.session_type;
            const isVoid = props.row.original.status === 'Void';
            return (
              <Box minW={'3.5rem'}>
                <Tooltip
                  content={props.row.original.product}
                  aria-label="A tooltip"
                  interactive
                  contentProps={{ css: { '--tooltip-bg': 'gray' } }}
                >
                  <Center
                    px={1}
                    py={1}
                    fontWeight="medium"
                    rounded="md"
                    color={
                      isVoid ? 'gray.50' : getSessionColor(sessionType).color
                    }
                    border={
                      isVoid
                        ? '1px solid gray'
                        : getSessionColor(sessionType).borderColor
                    }
                    bg={
                      isVoid ? 'gray.100' : getSessionColor(sessionType).bgColor
                    }
                    maxW={'fit-content'}
                  >
                    {sessionType}
                  </Center>
                </Tooltip>
              </Box>
            );
          },
          header: 'Type',
          id: 'Session Type',
        }),
        columnHelper.accessor('total_hours', {
          cell: (info) => <Box minW={'8rem'}>{info.getValue()}</Box>,
          header: 'Duration (Mins)',
          id: 'Total Hours',
        }),
        // columnHelper.display({
        //   id: 'slp',
        //   cell: (props) => {
        //     const isVoid = props.row.original.status === 'Void';
        //     return (
        //       <Box color={isVoid ? 'gray.100' : 'black'}>
        //         {props.row.original?.slp?.first_name ||
        //         props.row.original?.slp?.last_name
        //           ? `${props.row.original?.slp?.first_name || ''} ${
        //               props.row.original?.slp?.last_name || ''
        //             }`.trim()
        //           : props.row.original?.slp}
        //       </Box>
        //     );
        //   },
        //   header: 'SLP',
        // }),
        columnHelper.accessor('product', {
          id: 'product',
          header: 'Product',
          enableSorting: true,
          cell: (props) => (
            <Box minW="6rem" maxW="10rem">
              <Tooltip
                content={props.row.original.product}
                showArrow
                contentProps={{
                  maxW: '200px',
                  whiteSpace: 'normal',
                  wordBreak: 'break-word',
                }}
              >
                <Text
                  whiteSpace="nowrap"
                  overflow="hidden"
                  textOverflow="ellipsis"
                  fontWeight="medium"
                  cursor="pointer"
                >
                  {props.row.original.product}
                </Text>
              </Tooltip>
            </Box>
          ),
        }),
        columnHelper.accessor('memo', {
          cell: (info) => {
            const isVoid = info.row.original.status === 'Void';
            return (
              <Box color={isVoid ? 'gray.100' : 'black'}>{info.getValue()}</Box>
            );
          },
          header: 'Memo',
          id: 'memo',
        }),

        columnHelper.display({
          id: 'slp_note',
          header: 'SLP Note',
          cell: (props) => (
            <Box>
              {props.row.original?.slp_notes?.length > 0 ? (
                <LinkedInvoiceDetails
                  data={{
                    invoices: props.row.original,
                    id: props.row.original?.slp_notes?.[0]?.id,
                  }}
                />
              ) : (
                <LinkSlpModal invoice={props.row.original} />
              )}
            </Box>
          ),
        }),
      ]
    : [
        columnHelper.accessor('amount_due', {
          cell: (info) => {
            const amountDue = Number(info.getValue());
            return (
              <Box>
                {amountDue < 0
                  ? `(${formatMoney(Math.abs(amountDue), {
                      currencyCode: info.row.original?.currency_code,
                    })})`
                  : formatMoney(amountDue, {
                      currencyCode: info.row.original?.currency_code,
                    })}
              </Box>
            );
          },
          header: 'Amount Due',
          id: 'amount_due',
        }),
      ];

  // Actions column
  const actionsColumn = columnHelper.display({
    id: 'edit-slp',
    cell: (props) => {
      const isVoid = props.row.original.status === 'Void';
      return (
        <Box data-no-row-click="true">
          <EditInvoice
            invoice={props.row.original}
            color={isVoid ? 'gray.100' : 'rgb(79 70 229)'}
            clientId={clientId}
          />
        </Box>
      );
    },
    header: 'Actions',
  });

  // Return columns in the desired order
  if (isSpeakFluent) {
    return [
      InvoiceDateColumn,
      invoiceNumberColumn,
      clientNameColumn,
      amountColumn,
      ...speakFluentColumns,
      actionsColumn,
    ];
  } else {
    const columns: any[] = [statusColumn];

    //show due date based on filter
    if (showUnpaidColumns) {
      // Show both Invoice Date and Due Date for unpaid invoices
      columns.push(InvoiceDateColumn, dueDateColumn);
    } else {
      // Show only Due Date for all invoices
      columns.push(dueDateColumn);
    }

    columns.push(
      invoiceNumberColumn,
      clientNameColumn,
      amountColumn,
      ...speakFluentColumns,
      actionsColumn
    );

    return columns;
  }
};
