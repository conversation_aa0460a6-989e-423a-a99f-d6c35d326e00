// app/api/stripe/connect/route.ts
import { generateStripeConnectUrl } from '@/lib/stripe';
import { createSupabaseServer } from '@/lib/supabase/server';
import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  const searchParams = request.nextUrl.searchParams;
  const user_id = searchParams.get('user_id');
  const supabase = createSupabaseServer();
  const {
    data: { user },
  } = await supabase.auth.getUser();
  if (!user)
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  console.log('user is ', user);

  // Use user ID as state for simplicity/CSRF (or generate random)
  const state = user_id!;
  const connectUrl = generateStripeConnectUrl(state);

  return NextResponse.redirect(connectUrl);
}
