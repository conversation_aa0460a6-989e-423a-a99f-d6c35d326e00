import { useUpdateInvoiceMutation } from '@/api/invoices/update-invoice';
import { useDeleteTransactionMutation } from '@/api/transactions/delete-transaction';
import { useLinkInvoiceItemHook } from '@/app/(dashboard)/invoices/newSFInvoiceFlow/_hook/useLinkInvoiceItemHook';
import { ConsentDialog } from '@/components/elements/dialog/ConsentDialog';
import { formatMoney } from '@/components/elements/format-money/FormatMoney';
import { CustomModal } from '@/components/elements/modal/custom-modal';
import { Button } from '@/components/ui/button';
import { queryKey } from '@/constants/query-key';
import { TCreateInvoiceHook } from '@/hooks/slp/useCreateInvoiceHook';
import { determineInvoiceStatus } from '@/reuseables/invoice/helpers';
import {
  Badge,
  Box,
  // Center,
  Flex,
  MenuContent,
  MenuItem,
  MenuRoot,
  MenuSeparator,
  MenuTrigger,
  // Icon,
  Text,
  chakra,
  useDisclosure,
} from '@chakra-ui/react';
import { useQueryClient } from '@tanstack/react-query';
import moment from 'moment';
import { Fragment, useState } from 'react';
import { BsThreeDotsVertical } from 'react-icons/bs';
import {
  IoCalendarOutline,
  IoReceiptOutline,
  IoWalletOutline,
} from 'react-icons/io5';
import { MdOutlineAccountBalanceWallet } from 'react-icons/md';
import EditPaymentForm from './EditPaymentForm';
import PaymentForm from './PaymentForm';

export default function Payment({
  //   soapNoteHook,
  // section,
  // abbr,
  initialBooking,
  amountDue = 0,
  resolvedAmountDue = '',
}: {
  soapNoteHook: TCreateInvoiceHook;
  //   section: 'slp' | 'client';
  abbr?: any;
  initialBooking: any;
  amountDue: number;
  resolvedAmountDue: string;
}) {
  //   console.log('initialBooking is ', initialBooking);
  const paymentDisclosure = useDisclosure();
  const EditPaymentDisclosure = useDisclosure();
  const queryClient = useQueryClient();
  const { transactions } = initialBooking?.slp_notes?.invoice ?? {};

  console.log('transactions', transactions);

  const linkTransactionHook = useLinkInvoiceItemHook({
    invoice_id: initialBooking?.slp_notes?.invoice?.id,
  });

  // const isPackage = Boolean(package_used?.[0]);

  const {
    mutateAsync: DeleteTransaction,
    isLoading: DeleteTransactionLoading,
  } = useDeleteTransactionMutation();
  const deleteTransactionDisclosure = useDisclosure();
  const [selectedTransaction, setSelectedTransaction] = useState(null) as any;
  const { mutateAsync: updateInvoices } = useUpdateInvoiceMutation();

  // delete transaction
  const handleRemoveTransaction = async () => {
    if (!selectedTransaction?.id) {
      return;
    }
    await DeleteTransaction(selectedTransaction?.id);
    const statusData = {
      transactions: [
        ...initialBooking.slp_notes.invoice.transactions.filter(
          (transaction: any) => transaction.id !== selectedTransaction.id
        ),
      ],
      total_price: initialBooking.slp_notes.invoice.total_price,
      tax_value: 0,
      discount: initialBooking.slp_notes.invoice.discount,
    };
    const invoiceStatus = determineInvoiceStatus(statusData);
    await updateInvoices({
      data: { status: invoiceStatus },
      id: initialBooking?.slp_notes?.invoice?.id,
    });
    deleteTransactionDisclosure.onClose();
    setSelectedTransaction(null);
    await queryClient.invalidateQueries({
      queryKey: [queryKey.bookings.getById, Number(initialBooking?.id)],
    });
    await queryClient.invalidateQueries({
      queryKey: [queryKey.transactions.getAll],
    });
    await queryClient.invalidateQueries([
      queryKey.client.getActivities,
      initialBooking?.client_id,
    ]);
  };

  return (
    <Box
      border={'1px solid #FEFEFE'}
      boxShadow={'lg'}
      rounded={'12px'}
      py={'1.5rem'}
      px={'10px'}
      w={'full'}
      minH={'6rem'}
      position={'relative'}
      maxH={'fit-content'}
    >
      <Box display={'flex'} width={'full'} gap={'1.25rem'} overflow={'hidden'}>
        <Box
          rounded={'full'}
          fontSize={'18px'}
          display={'flex'}
          justifyContent={'center'}
          alignItems={'center'}
          minW={'36px'}
          w={'36px'}
          h={'36px'}
          maxW={'36px'}
          cursor={'pointer'}
          color={'#E97A5B'}
          border={'2px solid #E97A5B'}
        >
          <IoWalletOutline />
        </Box>

        <Box overflow={'hidden'} width={'full'} maxWidth={'full'}>
          <Text color={'GrayText'}>Manage Payments</Text>
          {initialBooking?.slp_notes?.invoice?.id ? (
            <Box>
              <Flex gap={'.5rem'} mt={'1rem'} alignItems={'center'}>
                <Text>
                  <b> Amount Due:</b> {resolvedAmountDue}
                </Text>
                {/* {!initialTransaction && ( */}
                {amountDue > 0 && (
                  <Flex gap={'.5rem'} alignItems={'center'}>
                    <Box w={'1rem'} h={'1px'} bg={'black'}></Box>

                    <Text>
                      <chakra.span
                        cursor={'pointer'}
                        color={'primary.500'}
                        fontWeight={500}
                        onClick={paymentDisclosure.onOpen}
                      >
                        Record a payment
                      </chakra.span>{' '}
                      manually
                    </Text>
                  </Flex>
                )}
                {amountDue === 0 && transactions && transactions?.length ? (
                  <Text ml={'auto'}>
                    {' '}
                    <b>Status:</b> Your invoice is paid in full
                  </Text>
                ) : null}
              </Flex>
              {initialBooking?.slp_notes?.invoice?.id &&
                transactions &&
                transactions?.length > 0 && (
                  <Box mt={4}>
                    <Flex alignItems={'center'} gap={'0.5rem'} mb={'1rem'}>
                      <IoReceiptOutline color={'#ef9079'} size={16} />
                      <Text
                        fontSize={'md'}
                        fontWeight={600}
                        color={'primary.500'}
                      >
                        Transaction History
                      </Text>
                    </Flex>

                    {transactions.map((transaction: any) => (
                      <Fragment key={transaction.id}>
                        <Box
                          border={'1px solid'}
                          borderColor={'gray.50'}
                          rounded={'8px'}
                          p={'1rem'}
                          mb={'0.75rem'}
                          bg={'white'}
                          shadow={'md'}
                        >
                          <Flex alignItems={'start'} gap={'0.75rem'}>
                            {/* Transaction Icon */}
                            <Box
                              w={'32px'}
                              h={'32px'}
                              bg={'primary.50'}
                              border={'1px solid'}
                              borderColor={'primary.200'}
                              rounded={'6px'}
                              display={'flex'}
                              alignItems={'center'}
                              justifyContent={'center'}
                              flexShrink={0}
                            >
                              <MdOutlineAccountBalanceWallet
                                color={'#ef9079'}
                                size={16}
                              />
                            </Box>

                            {/* Transaction Details */}
                            <Box flex={1}>
                              <Flex
                                justifyContent={'space-between'}
                                alignItems={'start'}
                              >
                                <Box flex={1}>
                                  {/* Amount and Payment Method Badge */}
                                  <Flex
                                    alignItems={'center'}
                                    gap={'0.75rem'}
                                    mb={'0.5rem'}
                                  >
                                    <Text
                                      fontWeight={600}
                                      color={'gray.900'}
                                      fontSize={'md'}
                                    >
                                      {formatMoney(transaction?.amount, {
                                        currencyCode:
                                          initialBooking?.slp_notes?.invoice
                                            ?.currency_code,
                                      })}
                                    </Text>

                                    <Badge
                                      variant="outline"
                                      color="primary.600"
                                      borderColor="primary.300"
                                    >
                                      {transaction?.payment_method
                                        ?.toLowerCase()
                                        .replace(/_/g, ' ')
                                        .replace(/\b\w/g, (l: string) =>
                                          l.toUpperCase()
                                        )}
                                    </Badge>
                                  </Flex>
                                </Box>

                                {/* Menu */}
                                <MenuRoot>
                                  <MenuTrigger cursor={'pointer'}>
                                    <Box p={'0.25rem'} rounded={'4px'}>
                                      <BsThreeDotsVertical
                                        size={14}
                                        color={'gray.400'}
                                      />
                                    </Box>
                                  </MenuTrigger>
                                  <MenuContent
                                    cursor={'pointer'}
                                    position={'absolute'}
                                    zIndex={10}
                                    right={0}
                                  >
                                    <MenuItem value="edit">
                                      <Button
                                        p={'0'}
                                        h={'1rem'}
                                        fontWeight={600}
                                        color={'primary.500'}
                                        bg={'transparent !important'}
                                        outline={'none !important'}
                                        justifyContent={'start'}
                                        w={'full'}
                                        onClick={() => {
                                          setSelectedTransaction(transaction);
                                          EditPaymentDisclosure.onOpen();
                                        }}
                                      >
                                        Edit payment
                                      </Button>
                                    </MenuItem>
                                    <MenuSeparator />
                                    <MenuItem value="delete">
                                      <Button
                                        p={'0'}
                                        h={'1rem'}
                                        fontWeight={600}
                                        color={'red.500'}
                                        bg={'transparent !important'}
                                        outline={'none !important'}
                                        justifyContent={'start'}
                                        w={'full'}
                                        onClick={() => {
                                          setSelectedTransaction(transaction);
                                          deleteTransactionDisclosure.onOpen();
                                        }}
                                      >
                                        Remove Payment
                                      </Button>
                                    </MenuItem>
                                  </MenuContent>
                                </MenuRoot>
                              </Flex>

                              {/* Date */}
                              <Flex
                                alignItems={'center'}
                                gap={'0.5rem'}
                                mt={'0.25rem'}
                              >
                                <IoCalendarOutline
                                  size={12}
                                  color={'gray.400'}
                                />
                                <Text fontSize={'xs'} color={'gray.500'}>
                                  {moment(
                                    transaction?.transaction_date.split('T')[0]
                                  ).format('MMM D, YYYY')}
                                </Text>
                              </Flex>

                              {/* Internal Memo */}
                              {transaction?.note && (
                                <Box
                                  mt={'0.75rem'}
                                  p={'0.5rem'}
                                  bg={'primary.25'}
                                  rounded={'4px'}
                                  border={'1px solid'}
                                  borderColor={'primary.100'}
                                >
                                  <Text
                                    fontSize={'xs'}
                                    color={'gray.600'}
                                    fontWeight={500}
                                    mb={'0.25rem'}
                                  >
                                    Notes:
                                  </Text>
                                  <Text fontSize={'xs'} color={'gray.700'}>
                                    {transaction.note}
                                  </Text>
                                </Box>
                              )}
                            </Box>
                          </Flex>
                        </Box>
                      </Fragment>
                    ))}
                  </Box>
                )}
            </Box>
          ) : (
            <Text mt={'0.5rem'}>Please create an invoice</Text>
          )}
        </Box>
      </Box>

      {/* payment form */}
      <CustomModal
        w={{ base: '90%', md: '50%' }}
        open={paymentDisclosure.open}
        onOpenChange={paymentDisclosure.onClose}
      >
        <PaymentForm
          initialBooking={initialBooking}
          createPaymentDisclosure={paymentDisclosure}
          amountDue={amountDue}
          linkTransactionHook={linkTransactionHook}
        />
      </CustomModal>

      {/* Edit payment form */}
      <CustomModal
        w={{ base: '90%', md: '50%' }}
        open={EditPaymentDisclosure.open}
        onOpenChange={EditPaymentDisclosure.onClose}
      >
        <EditPaymentForm
          initialBooking={initialBooking}
          selectedTransaction={selectedTransaction}
          editPaymentDisclosure={EditPaymentDisclosure}
          amountDue={amountDue}
        />
      </CustomModal>

      <ConsentDialog
        open={deleteTransactionDisclosure.open}
        onOpenChange={deleteTransactionDisclosure.onClose}
        handleSubmit={handleRemoveTransaction}
        note={`This action will remove this transaction from this invoice.`}
        isLoading={DeleteTransactionLoading}
        heading={'Are you sure?'}
        // firstBtnText="Back"
        // secondBtnText="Yes, Disconnect"
      />
    </Box>
  );
}
