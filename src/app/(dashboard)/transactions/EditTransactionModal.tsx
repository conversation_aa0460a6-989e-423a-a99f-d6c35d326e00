import { CustomModal } from '@/components/elements/modal/custom-modal';
import CustomSelect from '@/components/Input/CustomSelect';
import CustomTextArea from '@/components/Input/CustomTextArea';
import NumericInput from '@/components/Input/NumericInput';
import StringInput from '@/components/Input/StringInput';
import { Button } from '@/components/ui/button';
import {
  transactionMethodOptions,
  transactionTypeOptions,
} from '@/data/options';
import { Box, chakra, Flex, Stack } from '@chakra-ui/react';
import { useEditTransactionHook } from './_hook/useEditTransactionHook';

const EditTransactionModal = ({ transaction, onClose, isOpen }: any) => {
  //console.log('  transaction in EditTransactionModal', transaction);
  const {
    handleSubmit,
    values,
    // formatDate,
    loading,
    handleChange,
    setFieldValue,
    handleBlur,
  } = useEditTransactionHook(transaction, onClose);

  // console.log('values in EditTransactionModal', values);
  return (
    <div>
      <CustomModal w={'30rem'} onOpenChange={onClose} open={isOpen}>
        <Box>
          <chakra.form onSubmit={handleSubmit}>
            <Stack alignItems={'center'}>
              {/* <Box width={'100%'}>
              <label className="font-medium text-gray-900">Lookup Client</label>
              <SearchContact
                // value={
                //   user ? `${user?.first_name} ${user?.last_name}` : invoice.name
                // }
                setSearchResult={(e: any) => {
                  setSearchResult(e);
                }}
                selectExistingUser={selectExistingUser}
                searchResult={searchResult}
              />
            </Box> */}
              <StringInput
                inputProps={{
                  name: 'Name',
                  value: values.name || '',
                  readOnly: true,
                }}
                fieldProps={{
                  label: 'Client Name',
                }}
              />{' '}
              <StringInput
                inputProps={{
                  name: 'transaction_date',
                  type: 'date',
                  placeholder: 'Select Date',
                  onBlur: handleBlur,
                  value: values.transaction_date
                    ? values.transaction_date?.split('T')[0] || ''
                    : '',
                  onChange: handleChange,
                }}
                fieldProps={{
                  label: 'Date',
                  required: true,
                }}
              />
              <NumericInput
                inputProps={{
                  name: 'Amount',
                  placeholder: 'Add Amount',
                  value: values.amount || '',
                }}
                onValueChange={(item: any) => {
                  setFieldValue('amount', item.floatValue);
                }}
                fieldProps={{
                  label: 'Amount',

                  //   invalid: touched.title && !!errors.title,
                  //   errorText: errors.title,
                  required: true,
                }}
              />{' '}
              <Box width={'100%'}>
                <CustomSelect
                  placeholder="Transaction Method"
                  required={true}
                  defaultValue={transactionMethodOptions.find(
                    (item) =>
                      item.value?.toLocaleLowerCase() ===
                      values?.payment_method?.toLocaleLowerCase()
                  )}
                  onChange={(val) =>
                    setFieldValue('payment_method', val?.value)
                  }
                  options={transactionMethodOptions}
                  label="Transaction Method"
                  // defaultValue={statusOptions?.find(
                  //   (item) => item.value?.toLocaleLowerCase() === 'active'
                  // )}
                />
              </Box>
              <Box width={'100%'}>
                <CustomSelect
                  placeholder="Transaction Type"
                  defaultValue={transactionTypeOptions.find(
                    (item) =>
                      item.value?.toLocaleLowerCase() ===
                      values?.transaction_type?.toLocaleLowerCase()
                  )}
                  required={true}
                  onChange={(val) =>
                    setFieldValue('transaction_type', val?.value)
                  }
                  options={transactionTypeOptions}
                  label="Transaction Type"
                  // defaultValue={statusOptions?.find(
                  //   (item) => item.value?.toLocaleLowerCase() === 'active'
                  // )}
                />
              </Box>
              <CustomTextArea
                inputProps={{
                  name: 'Note',
                  value: values.note,
                  onChange: (e) => {
                    setFieldValue('note', e.target.value);
                  },
                }}
                fieldProps={{
                  label: 'Note',

                  //    invalid: touched.description && !!errors.description,
                }}
              />
              <Flex
                my={'1.8rem'}
                alignItems={'center'}
                justifyContent={'space-between'}
                width={'100%'}
              >
                <Button
                  onClick={onClose}
                  variant={'outline'}
                  minH={'3rem'}
                  minW={'15rem'}
                >
                  Cancel{' '}
                </Button>
                <Button
                  loading={loading}
                  minH={'3rem'}
                  minW={'15rem'}
                  type="submit"
                  bg={'primary.500'}
                >
                  Save
                </Button>
              </Flex>
            </Stack>
          </chakra.form>
        </Box>
      </CustomModal>
    </div>
  );
};

export default EditTransactionModal;
