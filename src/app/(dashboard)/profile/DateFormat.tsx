import React, { useState, useMemo } from 'react';
import { Box, Text, VStack, HStack, Input, Icon } from '@chakra-ui/react';
import {
  FiCalendar,
  FiInfo,
  FiAlertCircle,
  FiCheckCircle,
} from 'react-icons/fi';
import moment from 'moment';
import CustomSelect from '@/components/Input/CustomSelect';

// Common separators allowed in date formats
const VALID_SEPARATORS = [' ', '/', '-', '.', ',', ':', ';', '_', '|'];

/**
 * Validates if a character sequence is valid for moment.js format
 */
const isValidMomentSequence = (
  input: string,
  newChar: string,
  position: number
): boolean => {
  // Allow valid separators
  if (VALID_SEPARATORS.includes(newChar)) return true;

  // Allow valid moment characters
  if (!/[yYdDmMo]/.test(newChar)) return false;

  // If it's the first character, allow any valid moment character
  if (position === 0) return /[yYdDmM]/.test(newChar);

  const prevChar = input[position - 1];

  // Handle 'o' - only valid after 'D' (for Do)
  if (newChar === 'o') {
    return ['D', 'd', 'M', 'm'].includes(prevChar);
  }

  // If previous character is a separator, allow any valid moment character
  if (VALID_SEPARATORS.includes(prevChar)) {
    return /[yYdDmM]/.test(newChar);
  }

  // If previous character is the same, allow repetition (DD, MM, YY, etc.)
  if (prevChar?.toLowerCase() === newChar?.toLowerCase()) return true;

  // If previous character is 'D' and new character is 'o', allow it (Do)
  if (['D', 'd', 'M', 'm'].includes(prevChar) && newChar?.toLowerCase() === 'o')
    return true;

  // Otherwise, don't allow mixing different characters (YD, YM, etc.)
  return false;
};

/**
 * Validates the entire format string
 */
const validateMomentFormat = (
  format: string
): { isValid: boolean; error?: string } => {
  if (!format.trim()) {
    return { isValid: false, error: 'Format cannot be empty' };
  }

  // Test if moment can parse the format
  try {
    const testDate = new Date();
    const formatted = moment(testDate).format(format);

    // If formatting returns the same string as input, it might be invalid
    if (formatted === format) {
      return { isValid: false, error: 'Invalid format - no date tokens found' };
    }

    return { isValid: true };
  } catch (error) {
    return { isValid: false, error: 'Invalid moment.js format' };
  }
};

/**
 * Filters input to only allow valid moment.js character sequences
 */
const filterMomentInput = (input: string): string => {
  let result = '';

  for (let i = 0; i < input.length; i++) {
    const char = input[i];

    // Check if this character is valid in this position
    if (isValidMomentSequence(result, char, i)) {
      result += char;
    }
  }

  return result;
};

// Format options for CustomSelect
const formatTypes = [
  {
    value: 'MM/DD/YYYY',
    type: 'US format',
  },
  {
    value: 'DD/MM/YYYY',
    type: 'European format',
  },
  {
    value: 'YYYY-MM-DD',
    type: 'ISO format',
  },
  {
    value: 'DD MMM YYYY',
    type: 'Long format',
  },
  {
    value: 'MMM DD, YYYY',
    type: 'US long format',
  },
  {
    value: 'DD.MM.YYYY',
    type: 'German format ',
  },
  // {
  //   value: 'CUSTOM',
  //   type: 'Custom Format - Define your own format',
  // },
];
const formatOptions = formatTypes
  .map((item: any) => {
    return {
      value: item.value,
      label: (
        <Box>
          <Box>{item.value}</Box>
          <Box>{`${item.type} (${moment().format(item.value)})`}</Box>
        </Box>
      ),
    };
  })
  .concat([
    {
      value: 'CUSTOM',
      label: (
        <Box>
          <Box>Custom Format</Box>
          <Box>Define your own format</Box>
        </Box>
      ),
    },
  ]);

const DateFormat = ({ slpHook }: any) => {
  const { handleChange, payload } = slpHook;
  const [isCustom, setIsCustom] = useState(false);
  const [customFormat, setCustomFormat] = useState('');
  const [validationError, setValidationError] = useState<string>('');

  // Check if current format is custom (not in predefined list)
  const currentFormat = payload?.date_format || '';
  const isCurrentCustom = !formatOptions.some(
    (f) => f.value === currentFormat && f.value !== 'CUSTOM'
  );

  // Initialize custom state based on current format
  React.useEffect(() => {
    if (isCurrentCustom && currentFormat) {
      setIsCustom(true);
      setCustomFormat(currentFormat);
    }
  }, [currentFormat, isCurrentCustom]);

  // Generate live preview
  const livePreview = useMemo(() => {
    const formatToUse = isCustom ? customFormat : currentFormat;
    if (!formatToUse) return '';

    try {
      return moment().format(formatToUse);
    } catch (error) {
      return 'Invalid format';
    }
  }, [isCustom, customFormat, currentFormat]);

  console.log('customFormat', customFormat);

  const handleFormatSelect = (option: any) => {
    if (option.value === 'CUSTOM') {
      setIsCustom(true);
      setCustomFormat(currentFormat || 'DD-MM-YYYY');
      handleChange(currentFormat || 'DD-MM-YYYY', 'date_format');
    } else {
      setIsCustom(false);
      setCustomFormat('');
      handleChange(option.value, 'date_format');
    }
  };

  const formatChar = ['M', 'm', 'D', 'd', 'Y', 'y'];

  const handleCustomFormatChange = (value: string) => {
    // Filter input to only allow valid moment.js characters
    const filteredValue = filterMomentInput(value);

    // Validate the format
    const validation = validateMomentFormat(filteredValue);
    const newFilteredValue = filteredValue
      ? filteredValue
          ?.split('')
          ?.map((item: any) => {
            if (formatChar.includes(item)) {
              return item?.toUpperCase();
            }
            if (['O', 'o'].includes(item)) {
              return item?.toLowerCase();
            }
            return item;
          })
          ?.join('')
      : filteredValue;
    setCustomFormat(newFilteredValue);
    setValidationError(validation.error || '');

    // Only update the parent if the format is valid
    if (validation.isValid) {
      handleChange(newFilteredValue, 'date_format');
    }
  };

  // Get selected option for CustomSelect
  const getSelectedOption = () => {
    if (isCustom) {
      return formatOptions.find((opt) => opt.value === 'CUSTOM');
    }
    return formatOptions.find((opt) => opt.value === currentFormat);
  };

  return (
    <Box bg="gray.50" p={6} borderRadius="lg">
      {/* Header */}
      <VStack align="stretch" gap={4} mb={6}>
        <HStack>
          <Icon as={FiCalendar} color="gray.600" />
          <Text fontWeight="600" fontSize="lg">
            Date Format Settings
          </Text>
        </HStack>
        <Text fontSize="sm" color="gray.600">
          Customize how dates are displayed across your organizations system.
          Changes will apply to all users and reports.
        </Text>
      </VStack>

      {/* Format Selection */}
      <VStack align="stretch" gap={4} mb={6}>
        <HStack>
          <Icon as={FiInfo} color="gray.500" boxSize={4} />
          <Text fontWeight="500" color="gray.700">
            Format Selection
          </Text>
        </HStack>
        <Text fontSize="sm" color="gray.600">
          Select your preferred date format from the options below
        </Text>

        {/* Date Format CustomSelect */}
        <Box w={{ base: 'auto', md: '45%' }}>
          <CustomSelect
            onChange={handleFormatSelect}
            selectedOption={getSelectedOption()}
            options={formatOptions}
            label="Date Format"
          />
        </Box>

        {/* Custom Format Input */}
        {isCustom && (
          <Box>
            <Text fontSize="sm" fontWeight="500" color="gray.700" mb={2}>
              Custom Format
            </Text>
            <Box position="relative">
              <Input
                value={customFormat}
                onChange={(e) => handleCustomFormatChange(e.target.value)}
                placeholder="DD-MM-YYYY"
                bg="white"
                border="1px solid"
                borderColor={validationError ? 'red.300' : 'blue.300'}
                _focus={{
                  borderColor: validationError ? 'red.500' : 'blue.500',
                  boxShadow: validationError
                    ? '0 0 0 1px red.500'
                    : '0 0 0 1px blue.500',
                }}
                type="text"
                pr={10}
              />
              {/* Validation Icon */}
              <Box
                position="absolute"
                right={3}
                top="50%"
                transform="translateY(-50%)"
              >
                {customFormat && (
                  <Icon
                    as={validationError ? FiAlertCircle : FiCheckCircle}
                    color={validationError ? 'red.500' : 'green.500'}
                    boxSize={4}
                  />
                )}
              </Box>
            </Box>

            {/* Validation Error Message */}
            {validationError && (
              <HStack mt={1} gap={1}>
                <Icon as={FiAlertCircle} color="red.500" boxSize={3} />
                <Text fontSize="xs" color="red.500">
                  {validationError}
                </Text>
              </HStack>
            )}

            {/* Helper Text */}
            <Text fontSize="xs" color="gray.500" mt={1}>
              Valid tokens: DD (day), MM (month), YYYY/YY (year), Do (ordinal
              day)
              <br />
              Rules: Characters can repeat (DD, MM) or be followed by
              separators. Only D can be followed by o for ordinal.
              <br />
              Separators: / - . , : ; _ | (space)
            </Text>
          </Box>
        )}
      </VStack>

      {/* Live Preview */}
      <Text fontSize="sm" fontWeight="500" color="gray.700" mb={2}>
        Live Preview
      </Text>
      <Box
        p={4}
        bg="white"
        border="3px"
        borderColor="gray.50"
        borderRadius="md"
        borderStyle="dashed"
      >
        <HStack justify="space-between" align="center">
          <Text fontSize="sm" color="gray.600">
            Current date will display as:
          </Text>
          <Box
            px={3}
            py={1}
            bg="gray.500"
            color="white"
            borderRadius="md"
            fontSize="sm"
            fontWeight="500"
          >
            {livePreview || 'Select a format'}
          </Box>
        </HStack>
      </Box>
    </Box>
  );
};

export default DateFormat;
