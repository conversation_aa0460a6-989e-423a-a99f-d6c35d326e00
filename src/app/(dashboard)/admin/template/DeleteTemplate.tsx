import { Box } from '@chakra-ui/react';
import React from 'react';
import { useEditTemplateHook } from '@/hooks/admin/template/useEditTemplateHook';
import { ConsentDialog } from '@/components/elements/dialog/ConsentDialog';

type DeleteTemplateType = {
  templateHook: ReturnType<typeof useEditTemplateHook>;
};

export default function DeleteTemplate({ templateHook }: DeleteTemplateType) {
  const {
    loading,
    onDeleteClose,
    isDeleteOpen,
    // onDeleteOpen,
    handleDeleteTemplate,
    // values,
  } = templateHook;

  return (
    <>
      <Box
        fontWeight={500}
        // onClick={onDeleteOpen}
        cursor={'pointer'}
        color={'red'}
        minWidth={'100%'}
      >
        Delete
      </Box>
      {/* <CustomModal
        w={{ base: '90%', md: '40%' }}
        open={isDeleteOpen}
        onOpenChange={() => {
          if (loading.update) {
            return;
          }
          onDeleteClose();
        }}
      >
        <Box my={'1rem'}>
          <Text fontSize={'1.2rem'} textAlign={'center'} fontWeight={'bold'}>
            Delete Template
          </Text>
        </Box>
        <Box>
          <Stack pl={'.5rem'} gap={'0.5rem'} mt={1}>
            <Box position={'relative'}>
              <Text fontWeight={600} textAlign={'center'}>
                {' '}
                Are you sure you want to DELETE {values.name} template
              </Text>
              <Text textAlign={'center'} fontWeight={400} color={'#e97a5b'}>
                {' '}
                This Action is IRREVERSIBLE!
              </Text>
            </Box>
            <Flex
              my={'1.8rem'}
              alignItems={'center'}
              justifyContent={'space-between'}
            >
              <Button
                onClick={onDeleteClose}
                variant={'outline'}
                minH={'3rem'}
                minW={'15rem'}
                disabled={loading?.delete}
              >
                Cancel
              </Button>
              <Button
                loading={loading?.delete}
                disabled={loading?.delete}
                minH={'3rem'}
                bg={'red.500'}
                minW={'15rem'}
                type="button"
                onClick={handleDeleteTemplate}
              >
                Delete
              </Button>
            </Flex>
          </Stack>
        </Box>
      </CustomModal> */}
      <ConsentDialog
        handleSubmit={handleDeleteTemplate}
        open={isDeleteOpen}
        onOpenChange={onDeleteClose}
        heading={'Confirm deletion?'}
        note="This will permanently delete this Template."
        isLoading={loading.delete}
      />
    </>
  );
}
