import { CustomModal } from '@/components/elements/modal/custom-modal';
//import TextEditor from '@/components/Input/CustomEditor';
import StringInput from '@/components/Input/StringInput';
import { Box, Flex, Stack, Text } from '@chakra-ui/react';
import { Button } from '@/components/ui/button';
import React from 'react';
import { FormFieldError } from '@/components/Input/FormFieldErrors';
import { useEditTemplateHook } from '@/hooks/admin/template/useEditTemplateHook';
import TextEditorNew from '@/components/Input/NewTextEditor';

type EditTemplateType = {
  templateHook: ReturnType<typeof useEditTemplateHook>;
};

export default function EditTemplate({ templateHook }: EditTemplateType) {
  const {
    loading,
    onClose,
    isOpen,
    errors,
    handleChange,
    handleReplacementTag,
    // onOpen,
    handleSubmit,
    replacementTags,
    setFieldValue,
    // setValues,
    touched,
    values,
  } = templateHook;

  return (
    <>
      <Box
        fontWeight={500}
        cursor={'pointer'}
        color={'rgb(79 70 229)'}
        minWidth={'100%'}
      >
        Edit
      </Box>
      <CustomModal
        w={{ base: '90%', md: '50%' }}
        open={isOpen}
        onOpenChange={() => {
          if (loading.update) {
            return;
          }
          onClose();
        }}
      >
        <Box my={'1rem'}>
          <Text fontSize={'1.2rem'} textAlign={'center'} fontWeight={'bold'}>
            Edit {values.name} Template
          </Text>
        </Box>
        <Box>
          <form onSubmit={handleSubmit}>
            {' '}
            <Stack pl={'.5rem'} gap={'0.5rem'} mt={1}>
              <Box position={'relative'}>
                {/* <Text fontWeight={600}>Template </Text> */}
                <StringInput
                  inputProps={{
                    name: 'name',
                    value: values.name || '',
                    onChange: handleChange,
                  }}
                  fieldProps={{
                    invalid: touched.name && !!errors.name,
                    label: 'Template Name',
                    errorText: errors.name,
                  }}
                />
                <TextEditorNew
                  handleReplacementTag={handleReplacementTag}
                  replacementTags={replacementTags}
                  saveContent={(e: any) => {
                    setFieldValue('content', e);
                  }}
                  initialContent={values.content || ''}
                  initialPresent={false}
                />
                <FormFieldError
                  errors={errors}
                  name="content"
                  touched={touched}
                />
              </Box>
              <Flex
                my={'1.8rem'}
                alignItems={'center'}
                justifyContent={'space-between'}
              >
                <Button
                  onClick={onClose}
                  variant={'outline'}
                  minH={'3rem'}
                  minW={'15rem'}
                  disabled={loading?.update}
                >
                  Cancel
                </Button>
                <Button
                  loading={loading?.update}
                  disabled={loading?.update}
                  minH={'3rem'}
                  bg={'primary.500'}
                  minW={'15rem'}
                  type="submit"
                >
                  Save
                </Button>
              </Flex>
            </Stack>
          </form>
        </Box>
      </CustomModal>
    </>
  );
}
