import { Box } from '@chakra-ui/react';
import {
  MenuContent,
  MenuItem,
  MenuRoot,
  MenuTrigger,
} from '@/components/ui/menu';
import EditTemplate from './EditTemplate';
import { ITemplates } from '@/shared/interface/templates';
import DeleteTemplate from './DeleteTemplate';
import { BsThreeDotsVertical } from 'react-icons/bs';
import { useEditTemplateHook } from '@/hooks/admin/template/useEditTemplateHook';

export default function TemplateActions({ data }: { data: ITemplates }) {
  const templateHook = useEditTemplateHook({ row: data });
  return (
    <Box position={'relative'}>
      <MenuRoot>
        <MenuTrigger cursor={'pointer'}>
          <BsThreeDotsVertical />
        </MenuTrigger>
        <MenuContent cursor={'pointer'} position={'absolute'}>
          <MenuItem value="edit" onClick={templateHook.onOpen}>
            <EditTemplate templateHook={templateHook} />
          </MenuItem>
          {/* <MenuSeparator /> */}
          <MenuItem value="delete" onClick={templateHook.onDeleteOpen}>
            <DeleteTemplate templateHook={templateHook} />
          </MenuItem>
        </MenuContent>
      </MenuRoot>
    </Box>
  );
}
