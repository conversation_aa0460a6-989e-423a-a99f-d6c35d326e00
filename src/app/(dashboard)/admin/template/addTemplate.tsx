import { CustomModal } from '@/components/elements/modal/custom-modal';
//import TextEditor from '@/components/Input/CustomEditor';
import StringInput from '@/components/Input/StringInput';
import { useTemplateHook } from '@/hooks/admin/template/useTemplateHook';
import { Box, Flex, Stack, Text } from '@chakra-ui/react';

import { Button } from '@/components/ui/button';
import React from 'react';
import { FormFieldError } from '@/components/Input/FormFieldErrors';
import TextEditorNew from '@/components/Input/NewTextEditor';

export type useTemplateHookReturnType = ReturnType<typeof useTemplateHook>;

type AddTemplateType = {
  templateHook: useTemplateHookReturnType;
};

export default function AddTemplate({ templateHook }: AddTemplateType) {
  const {
    errors,
    handleChange,
    handleReplacementTag,
    handleSubmit,
    loading,
    // setValues,
    values,
    setFieldValue,
    touched,
    onClose,
    isOpen,
    replacementTags,
  } = templateHook;
  return (
    <CustomModal
      w={{ base: '90%', md: '50%' }}
      open={isOpen}
      onOpenChange={() => {
        if (loading.create) {
          return;
        }
        onClose();
      }}
    >
      <Box my={'1rem'}>
        <Text fontSize={'1.2rem'} textAlign={'center'} fontWeight={'bold'}>
          Add a new Template
        </Text>
      </Box>
      <Box>
        <form onSubmit={handleSubmit}>
          {' '}
          <Stack pl={'.5rem'} gap={'0.5rem'} mt={1}>
            <Box position={'relative'}>
              {/* <Text fontWeight={600}>Template </Text> */}
              <StringInput
                inputProps={{
                  name: 'name',
                  value: values.name || '',
                  onChange: handleChange,
                }}
                fieldProps={{
                  invalid: touched.name && !!errors.name,
                  label: 'Template Name',
                  errorText: errors.name,
                }}
              />
              <TextEditorNew
                handleReplacementTag={handleReplacementTag}
                replacementTags={replacementTags}
                saveContent={(e: any) => {
                  setFieldValue('content', e);
                }}
                initialContent={values.content || ''}
                initialPresent={false}
              />
              <FormFieldError
                errors={errors}
                name="content"
                touched={touched}
              />
            </Box>
            <Flex
              my={'1.8rem'}
              alignItems={'center'}
              justifyContent={'space-between'}
            >
              <Button
                onClick={onClose}
                variant={'outline'}
                minH={'3rem'}
                minW={'15rem'}
                disabled={loading?.create}
              >
                Cancel
              </Button>
              <Button
                loading={loading?.create}
                disabled={loading?.create}
                minH={'3rem'}
                bg={'primary.500'}
                minW={'15rem'}
                type="submit"
              >
                Save
              </Button>
            </Flex>
          </Stack>
        </form>
      </Box>
    </CustomModal>
  );
}
