'use client';
import soapLogo from '@/assets/soapLogo.png';
import StringInput from '@/components/Input/StringInput';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { ProgressBar, ProgressRoot } from '@/components/ui/progress';
import { Radio, RadioGroup } from '@/components/ui/radio';
import { Box, Flex, Image, Stack, Text, Textarea } from '@chakra-ui/react';
import { useMemo, useState } from 'react';

const PreviewForm = ({ formDetails }: { formDetails: any }) => {
  // State for form values
  const [formValues, setFormValues] = useState<Record<string, any>>({});
  const [currentPage, setCurrentPage] = useState(1);

  console.log('formDetails', formDetails);

  // Group questions by page
  const pages = useMemo(() => {
    const pageMap = new Map<number, any[]>();

    formDetails.questions.forEach((question: any) => {
      const page = question.page || 1;
      if (!pageMap.has(page)) {
        pageMap.set(page, []);
      }
      pageMap.get(page)!.push(question);
    });

    return Array.from(pageMap.keys()).sort((a, b) => a - b);
  }, [formDetails.questions]);

  const currentPageQuestions = useMemo(() => {
    return formDetails.questions.filter(
      (q: any) => (q.page || 1) === currentPage
    );
  }, [formDetails.questions, currentPage]);

  const progressPercentage = ((currentPage - 1) / (pages.length - 1)) * 100;
  const isSinglePage = pages.length === 1;
  const isFirstPage = currentPage === 1;
  const isLastPage = currentPage === pages.length;

  // Handle form value changes
  const handleFieldChange = (fieldName: string, value: any) => {
    setFormValues((prev) => ({
      ...prev,
      [fieldName]: value,
    }));
  };

  // Navigation functions
  const goToNextPage = () => {
    if (!isLastPage) {
      setCurrentPage((prev) => prev + 1);
    }
  };

  const goToPreviousPage = () => {
    if (!isFirstPage) {
      setCurrentPage((prev) => prev - 1);
    }
  };

  const handlePreviewSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (isLastPage) {
      // For preview, just show an alert or do nothing
      alert('This is a preview form. Submission is disabled.');
    } else {
      goToNextPage();
    }
  };

  return (
    <Box width={'100%'} p={4} h={'100%'}>
      {/* Preview Notice */}
      <Box
        mb={4}
        p={3}
        bg="primary.50"
        borderRadius="md"
        border="1px solid"
        borderColor="primary.200"
        textAlign="center"
      >
        <Text fontSize="sm" color="primary.700" fontWeight="500">
          Form Preview Mode - Submission is disabled
        </Text>
      </Box>

      {/* Progress Bar - Only show for multi-page forms */}
      {!isSinglePage && (
        <Box mb={6}>
          <Flex justify="space-between" align="center" mb={2}>
            <Text fontSize="sm" color="gray.600">
              Page {currentPage} of {pages.length}
            </Text>
          </Flex>

          <ProgressRoot
            shape="rounded"
            value={progressPercentage || 0}
            colorPalette="orange"
            size="lg"
          >
            <ProgressBar rounded=".9rem" />
          </ProgressRoot>
        </Box>
      )}

      {/* Form Title - Show for single page forms */}
      <Box mb={6} textAlign="center">
        <Text fontSize="2xl" fontWeight="bold" color="gray.700">
          {formDetails.title}
        </Text>
        {formDetails.description && (
          <Text fontSize="md" color="gray.600" mt={2}>
            {formDetails.description}
          </Text>
        )}
      </Box>

      {/* Form Content */}
      <form
        onSubmit={handlePreviewSubmit}
        style={{
          width: '90%',
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'center',
          alignItems: 'center',
          margin: '0 auto',
        }}
      >
        {/* Current Page Questions */}
        {currentPageQuestions.map((question: any) => {
          // Section - handle early without form field logic
          if (question.type === 'section') {
            return (
              <Box
                key={question.id}
                width="100%"
                mb={8}
                p={6}
                bg="primary.50"
                borderLeft="4px solid"
                borderLeftColor="primary.500"
                borderRadius="md"
                boxShadow="sm"
              >
                <Text fontSize="xl" fontWeight="700" color="black" mb={2}>
                  {question.title}
                </Text>
                {question.description && (
                  <Text fontSize="md" color="black" lineHeight="tall">
                    {question.description}
                  </Text>
                )}
              </Box>
            );
          }

          // Form field logic - only for actual form inputs
          const fieldName = `question_${question.id}`;
          const fieldValue = formValues[fieldName];

          // Textbox or Number Input
          if (question.type === 'Textbox' || question.type === 'Number') {
            return (
              <Box
                key={question.id}
                width="100%"
                mb={8}
                p={4}
                borderColor="gray.100"
                borderRadius="md"
                bg="white"
                boxShadow="sm"
              >
                {question.title && (
                  <Text fontSize="lg" fontWeight="600" color="gray.700" mb={2}>
                    {question.title}
                  </Text>
                )}

                {question.description && (
                  <Text
                    fontSize="sm"
                    color="gray.500"
                    mb={3}
                    lineHeight="short"
                  >
                    {question.description}
                  </Text>
                )}

                <Flex align="center" mb={3}>
                  <Text fontSize="md" fontWeight="500" color="gray.800">
                    {question.qt}
                  </Text>
                  {question.required && (
                    <Text as="span" color="red.500" ml={1}>
                      *
                    </Text>
                  )}
                </Flex>

                <StringInput
                  inputProps={{
                    name: fieldName,
                    type: question.type === 'Textbox' ? 'text' : 'number',
                    value: fieldValue || '',
                    onChange: (e) =>
                      handleFieldChange(fieldName, e.target.value),
                    placeholder: question.placeholder || '',
                  }}
                  fieldProps={{
                    required: question.required,
                  }}
                />
              </Box>
            );
          }

          // TextArea Input
          else if (question.type === 'TextArea') {
            return (
              <Box
                mb={9}
                key={question.id}
                width={'100%'}
                p={4}
                borderColor="gray.100"
                borderRadius="md"
                bg="white"
                boxShadow="sm"
              >
                {question.title && (
                  <Text fontSize="lg" fontWeight="600" color="gray.700" mb={2}>
                    {question.title}
                  </Text>
                )}

                {question.description && (
                  <Text
                    fontSize="sm"
                    color="gray.500"
                    mb={3}
                    lineHeight="short"
                  >
                    {question.description}
                  </Text>
                )}

                <Flex align="center" mb={3}>
                  <Text fontSize="md" fontWeight="500" color="gray.800">
                    {question.qt}
                  </Text>
                  {question.required && (
                    <Text as="span" color="red.500" ml={1}>
                      *
                    </Text>
                  )}
                </Flex>

                <Textarea
                  name={fieldName}
                  value={fieldValue || ''}
                  onChange={(e) => handleFieldChange(fieldName, e.target.value)}
                  placeholder={question.placeholder || ''}
                  minH="120px"
                  resize="vertical"
                />
              </Box>
            );
          }

          // Single Choice (Radio Buttons)
          else if (question.type === 'Single choice') {
            return (
              <Box
                mb={9}
                key={question.id}
                width={'100%'}
                p={4}
                borderColor="gray.100"
                borderRadius="md"
                bg="white"
                boxShadow="sm"
              >
                {question.title && (
                  <Text fontSize="lg" fontWeight="600" color="gray.700" mb={2}>
                    {question.title}
                  </Text>
                )}

                {question.description && (
                  <Text
                    fontSize="sm"
                    color="gray.500"
                    mb={3}
                    lineHeight="short"
                  >
                    {question.description}
                  </Text>
                )}

                <Text fontWeight="500" mb={2}>
                  {question.qt}
                  {question.required && (
                    <Text as="span" color="red.500">
                      *
                    </Text>
                  )}
                </Text>

                <RadioGroup
                  cursor={'pointer'}
                  name={fieldName}
                  onValueChange={(val) =>
                    handleFieldChange(fieldName, val.value)
                  }
                  value={fieldValue || ''}
                >
                  <Stack direction="column" gap={2}>
                    {question.options?.map((option: string, index: number) => (
                      <Radio key={index} value={option}>
                        <Text fontSize="12px" cursor={'pointer'}>
                          {option}
                        </Text>
                      </Radio>
                    ))}
                  </Stack>
                </RadioGroup>
              </Box>
            );
          }

          // Multiple Choice (Checkboxes)
          else if (question.type === 'Multiple choice') {
            return (
              <Box
                mb={9}
                key={question.id}
                width={'100%'}
                textTransform={'capitalize'}
                p={4}
                borderColor="gray.100"
                borderRadius="md"
                bg="white"
                boxShadow="sm"
              >
                {question.title && (
                  <Text fontSize="lg" fontWeight="600" color="gray.700" mb={2}>
                    {question.title}
                  </Text>
                )}

                {question.description && (
                  <Text
                    fontSize="sm"
                    color="gray.500"
                    mb={3}
                    lineHeight="short"
                  >
                    {question.description}
                  </Text>
                )}

                <Text fontWeight="500" mb={2}>
                  {question.qt}
                  {question.required && (
                    <Text as="span" color="red.500">
                      *
                    </Text>
                  )}
                </Text>

                <Stack direction="column" gap={2}>
                  {question.options?.map((option: string, index: number) => (
                    <Checkbox
                      key={index}
                      cursor={'pointer'}
                      colorPalette="gray"
                      checked={fieldValue?.includes(option) || false}
                      onChange={(e) => {
                        const currentValues = fieldValue || [];
                        const checked = (e.target as HTMLInputElement).checked;
                        const newValues = checked
                          ? [...currentValues, option]
                          : currentValues.filter(
                              (val: string) => val !== option
                            );
                        handleFieldChange(fieldName, newValues);
                      }}
                    >
                      <Text fontSize="12px" fontWeight={'450'}>
                        {option}
                      </Text>
                    </Checkbox>
                  ))}
                </Stack>
              </Box>
            );
          }

          // Date Picker
          else if (question.type === 'Date') {
            return (
              <Box
                mb={9}
                key={question.id}
                width={'100%'}
                p={4}
                borderColor="gray.100"
                borderRadius="md"
                bg="white"
                boxShadow="sm"
              >
                {question.title && (
                  <Text fontSize="lg" fontWeight="600" color="gray.700" mb={2}>
                    {question.title}
                  </Text>
                )}

                {question.description && (
                  <Text
                    fontSize="sm"
                    color="gray.500"
                    mb={3}
                    lineHeight="short"
                  >
                    {question.description}
                  </Text>
                )}

                <Text fontWeight="500" mb={2}>
                  {question.qt}
                  {question.required && (
                    <Text as="span" color="red.500">
                      *
                    </Text>
                  )}
                </Text>

                <StringInput
                  inputProps={{
                    name: fieldName,
                    onChange: (e) =>
                      handleFieldChange(fieldName, e.target.value),
                    value: fieldValue?.split('T')[0] || '', // Handle potential date string format
                    type: 'date',
                  }}
                  fieldProps={{
                    label: '', // You might want to make this dynamic if needed
                  }}
                />
              </Box>
            );
          }

          return null;
        })}

        {/* Navigation Buttons */}
        <Flex
          justify={isSinglePage ? 'center' : 'space-between'}
          width="100%"
          mt={6}
          mb={4}
        >
          {!isSinglePage && (
            <Button
              variant="outline"
              onClick={goToPreviousPage}
              disabled={isFirstPage}
              width="120px"
              type="button"
            >
              Previous
            </Button>
          )}

          <Button
            type="submit"
            colorScheme={isLastPage ? 'gray' : 'primary'}
            disabled={isLastPage}
            width={isSinglePage ? '200px' : '120px'}
          >
            {isSinglePage
              ? 'Preview Only'
              : isLastPage
                ? 'Preview Only'
                : 'Next'}
          </Button>
        </Flex>

        {/* Page Info */}
        <Text fontSize="sm" color="gray.500" textAlign="center">
          {isSinglePage
            ? 'This is a preview of your form. Submission is disabled.'
            : isLastPage
              ? 'This is the final page of your form preview.'
              : 'This is a preview mode. Navigate through the form to see all pages.'}
        </Text>
      </form>

      <Box
        display="flex"
        justifyContent="center"
        alignItems="center"
        width={'100%'}
        mt={6}
      >
        <Text fontSize={'12px'}>Powered by</Text>
        <Image src={soapLogo.src} alt="logo" w={'35'} h={'10'} ml={2} />
      </Box>
    </Box>
  );
};

export default PreviewForm;
